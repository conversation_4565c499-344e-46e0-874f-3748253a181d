<script setup lang="ts">
import {
  getLastClassExamReport,
  getLayerList,
  getSystemLevelList,
  oldTeacherTip,
} from '@/api/aiTask'
import {
  getStudentList,
  getTeacherClassList,
} from '@/api/taskCenter'
import { useAiTaskStore } from '@/stores/modules/aiTask'

const aiTaskStore = useAiTaskStore()
const route = useRoute()
const router = useRouter()
let isChecked = $ref<any>('')// 布置班级
let list = $ref<any>([]) // 班级选项list
let classType = $ref(1) // 默认行政班
const ref1 = ref<any>()// 引导提示ref
let open = ref<boolean>(false)// 引导提示弹框
let examId = $ref<string>('')
let reportId = $ref<string>('')
let layerList = $ref<any>([])// 分层数据
let activeLayer = $ref<any>('')// 分层选中项
let layerOpenStatus = $ref<boolean>(false)// 分层开关状态
let layerBtnDisabled = $ref(false)
// 系统分组数据
let systemLevelData = $ref<Array<{
  layerType: number
  layerTypeName: string
  studentList: Array<{
    schoolStudentId: number
    accountId: number
    idNum: string
    studentName: string
    headPicture: string
    studentScore?: string // 新增学生分数字段
  }>
}>>([])

const studentData = inject<Ref<any>>('studentData', ref({}))

async function handleRadioChecked() {
  getStudentListApi()
}

// 分层数据
async function getLayerListApi() {
  let res = await getLayerList()
  layerList = res?.map(it => ({
    ...it,
    name: it.title,
  })) ?? []
  if (examId)
    activeLayer = layerList?.[0].id
}
// 是否开启分成
async function getLastReportApi() {
  try {
    let res = await await getLastClassExamReport({
      schoolClassId: isChecked,
      sysSubjectId: route.query?.subjectId,
    })
    examId = res?.examId ?? ''
    reportId = res?.reportId ?? ''
    layerBtnDisabled = !examId
    if (examId) getSystemLevelListApi()
  }
  catch (error) {
    console.log(error)
  }
}
// 获取行政班级
async function getClassList() {
  try {
    let res = await getTeacherClassList({
      classType: 1,
      sysSubjectId: route.query?.subjectId,
    })
    list = res?.map((it) => {
      return {
        ...it,
        id: it.schoolClassId,
        name: `${it.sysGradeName}${it.className}`,
      }
    }) ?? []
    isChecked = list?.[0]?.id
    getStudentListApi()
  }
  catch (error) {
    console.log(error)
    list = []
  }
}
// 获取学生数据
async function getStudentListApi() {
  try {
    let res = await getStudentList({
      schoolClassId: isChecked,
      sysSubjectId: route.query?.subjectId,
    })
    studentData.value.selectStudentList = [
      ...new Set([
        ...res.map(v => v.schoolStudentId),
      ]),
    ]
  }
  catch (error) {
    console.log(error)
    studentData.value.selectStudentList = []
  }
}
// 跳转分层管理
function goPage() {
  router.push({
    name: 'HierarchicalMgt',
    query: {
      reportId,
      sysSubjectId: route.query?.subjectId,
      schoolClassId: isChecked,
    },
  })
}
// 用户第一次提示
async function oldTeacherTipApi() {
  try {
    let res = await oldTeacherTip({ type: 'LAYER_TEACHING' })
    if (res)
      open.value = true
  }
  catch (error) {
    console.log(error)
  }
}
async function getSystemLevelListApi() {
  try {
    if (!isChecked || !route.query?.subjectId || !reportId) return
    let res = await getSystemLevelList({
      schoolClassId: isChecked,
      sysSubjectId: route.query?.subjectId,
      reportId,
    })
    // 根据新的返回结构，从 layerList 中获取分组数据
    if (res && res.layerList)
      systemLevelData = res.layerList
    else
      systemLevelData = []
    aiTaskStore.studentLevelData = $g._.cloneDeep(systemLevelData)
  }
  catch (error) {
    console.log(error)
    systemLevelData = []
  }
}
// 监听 classType 变化，实时同步到 studentData
watch(
  () => classType,
  (newVal) => {
    // 只在有值时同步
    if (newVal != null && newVal !== undefined)
      studentData.value.classType = newVal
  },
  { immediate: true },
)

// 监听 activeLayer 变化，实时同步到 studentData
watch(
  () => activeLayer,
  (newVal) => {
    if (newVal != null && newVal !== undefined)
      studentData.value.layerType = newVal
  },
  { immediate: true },
)

// 监听 isChecked（班级选择）变化，实时同步到 studentData
watch(
  () => isChecked,
  (newVal) => {
    if (newVal != null && newVal !== undefined)
      studentData.value.classId = newVal
  },
  { immediate: true },
)

onBeforeMount(async () => {
  await getClassList()
  await getLastReportApi()
  await oldTeacherTipApi()
  await getLayerListApi()
})
onActivated(() => {
  let flag = aiTaskStore.getLayerStudents?.filter(it => it?.layerType)?.some(it => it?.schoolStudentIdList?.length)
  if (flag) {
    layerBtnDisabled = false
    activeLayer = layerList?.[0].id
  }
})
</script>

<template>
  <div>
    <div class=" bg-[#fff] br-[6px] py-10px px-17px mb-26px">
      <div class="flex item-center">
        <div class="w-[80px] text-16px h-fit  font-600 pt-6px">
          布置对象
        </div>

        <div class="flex-1 flex items-center flex-wrap" :class="{ '!my-10px': !$g.isPC }">
          <g-radio
            v-model="isChecked"
            :option="list"
            item-class="px-15px  border border-[#E6E6E6] bg-[#FBFBFB] text-[#666666] text-15px h-34px flex items-center br-[6px] mb-6px"
            active-item-class="!border-[#646AB4] !bg-[#ECEFFF]"
            show-marker
            @change="handleRadioChecked"
          />
        </div>
      </div>
    </div>
    <div class="p-17px bg-[#fff] br-[6px] mb-26px">
      <div class="flex items-center">
        <span class="text-16px leading-[24px] font-600">分层教学</span>
        <svg-common-question
          ref="ref1"
          class="w-13px h-13px mx-[7px]"
          @click="open = true"
        />
        <el-switch
          v-model="layerOpenStatus"
          size="small"
          :disabled="layerBtnDisabled"
        />
      </div>
      <div class="flex justify-between mt-[17px]">
        <g-radio
          v-model="activeLayer"
          class="flex-1"
          :option="layerList"
          item-class="px-16px py-[5px]  border border-[#6474FD] bg-[#fff] text-[#333] text-15px h-34px flex items-center br-[6px] mb-6px"
          active-item-class="!border-[#fff] !bg-[#EFF1FF] !text-[#6474FD]"
          :disabled="!layerOpenStatus"
        />
        <div class="!w-fit text-[#6474FD] text-[15px] leading-[34px] cursor-pointer " @click="goPage">
          分层管理
        </div>
      </div>
    </div>
    <el-tour
      v-model="open"
      :close-on-press-escape="false"
      :finish-button-props="{
        text: '完成导览', // 自定义结束按钮文案
        type: 'primary', // 可选按钮样式
      }"
      append-to="#app"
    >
      <template #indicators>
      </template>
      <el-tour-step
        :target="ref1?.$el"
        placement="right"
        :next-button-props="{
          children: '知道了',
          type: 'primary',
        }"
      >
        <div>
          学生分组模块可实现个性化教学，默认按最近考试成绩分高、中、低三组（分数范围可调整），不同分层课程内容不变、试题难度适配；模块默认关闭，若无考试数据且未手动分层时需先去 “分层设置” 完成分组才能开启，也可随时关闭，点击 “知道了” 即可使用。
        </div>
      </el-tour-step>
    </el-tour>
    <!-- <CustomSelectionDialog v-model:show="showDialog" :class-type="classType" /> -->
  </div>
</template>

<style scoped lang="scss">
:deep(.myCheckbox) {
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #74788d;
  }
  .el-checkbox__label {
    font-size: 15px !important;
  }
}
</style>
